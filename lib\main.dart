import 'dart:ui';

import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("IGME-340 Basic App")),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 150,
            width: double.infinity,
            color: Colors.yellow,
            child: Align(alignment: Alignment.centerLeft, child: Text("Item 01")),
          ),
          Container(
            height: 150,
            width: MediaQuery.of(context).size.width * 0.75,
            color: Colors.red,
            child: Align(alignment: Alignment.centerRight, child: Text("Item 02")),
          ),
          Container(
            height: 150,
            width: double.infinity,
            color: Colors.blue,
            child: Align(alignment: Alignment.centerLeft, child: Text("Item 03")),
          ),
          Container(
            height: 150,
            width: double.infinity,
            color: Colors.greenAccent,
            child: Align(alignment: Alignment.centerLeft, child: Text("Item 04")),
          ),
        ],
      ),
    );
  }
}
