import 'dart:ui';

import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("IGME-340 Basic App")),
      body: Padding(
        //Add padding to right of screen
        padding: const EdgeInsets.only(right: 50.0),
        child: Column(

          //Align containers in center
          mainAxisAlignment: MainAxisAlignment.center,

          //Align containers to the right
          crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            height: 150,

            //Get screen width
            width: MediaQuery.of(context).size.width,
            color: Colors.yellow,
            child: Align(alignment: Alignment.center, child: Text("Item 01")),
          ),
          Container(
            height: 150,
            width: MediaQuery.of(context).size.width * 0.75,
            color: Colors.red,
            child: Align(alignment: Alignment.center, child: Text("Item 02")),
          ),
          Container(
            height: 150,
            width: MediaQuery.of(context).size.width * 0.5,
            color: Colors.blue,
            child: Align(alignment: Alignment.center, child: Text("Item 03")),
          ),
          Container(
            height: 150,
            width: MediaQuery.of(context).size.width * 0.25,
            color: Colors.greenAccent,
            child: Align(alignment: Alignment.center, child: Text("Item 04")),
          ),
        ],
        ),
      ),
    );
  }
}
